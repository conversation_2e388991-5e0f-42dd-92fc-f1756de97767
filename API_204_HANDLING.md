# API 204 Response Handling

## Overview

This document describes the changes made to handle both 200 responses with empty lists and 204 (No Content) responses from the Go backend APIs.

## Problem

After rewriting the backend in Go, some APIs now return a 204 status code instead of a 200 response with an empty list when there's no data in the database. The frontend application needed to be updated to handle both cases gracefully.

## Solution

Added response interceptors to both axios instances (`axiosApi` and `api`) that automatically convert 204 responses to 200 responses with appropriate empty data structures.

## Files Modified

### 1. `src/modules/axios/axiosAPI.ts`
- Added `createEmptyResponse()` helper function
- Enhanced response interceptor to handle 204 responses
- Converts 204 responses to 200 with appropriate empty data structures

### 2. `src/services/api.ts`
- Added identical `createEmptyResponse()` helper function
- Enhanced response interceptor to handle 204 responses
- Ensures consistency across both API clients

## Endpoint Mappings

The interceptor handles different endpoints with appropriate empty data structures:

| Endpoint Pattern | Empty Data Structure |
|------------------|---------------------|
| `/orbstars/received` | `{ orbstars: { items: [], count: 0, page: 1, has_next: false, has_prev: false }, value_stats: {} }` |
| `/orbstars/given` | `{ orbstars: { items: [], count: 0, page: 1, has_next: false, has_prev: false }, value_stats: {} }` |
| `/orbstars` | `{ items: [], count: 0, page: 1, has_next: false, has_prev: false }` |
| `/users` | `[]` |
| `/scim/v2/Users` | `[]` |
| `/values` | `{ values: [] }` |
| `/orbstars/leaderboard` | `{}` |
| Default (unknown) | `[]` |

## How It Works

1. When an API call returns a 204 status code, the response interceptor catches it
2. The `createEmptyResponse()` function determines the appropriate empty data structure based on the endpoint URL
3. The response is modified to have a 200 status code with the appropriate empty data
4. The rest of the application continues to work without any changes

## Benefits

- **Backward Compatibility**: Existing code continues to work without modifications
- **Consistent Behavior**: Both 200 with empty data and 204 responses are handled identically
- **Centralized Handling**: All 204 response handling is done in the interceptors
- **Type Safety**: Maintains proper TypeScript types for all responses

## Testing

A test file `src/utils/api-204-handler.test.ts` has been created to demonstrate the functionality. You can run it to see how different endpoints are handled:

```bash
# Run the test (if you have ts-node installed)
npx ts-node src/utils/api-204-handler.test.ts
```

## Affected Components

The following components and stores automatically benefit from this change:

- **Orbstar Store** (`src/store/orbstars/index.ts`)
  - `fetchOrbstars()`
  - `fetchReceivedOrbstars()`
  - `fetchGivenOrbstars()`

- **User Store** (`src/store/user.ts`)
  - `getProfile()`

- **FormOrbstar Component** (`src/components/forms/FormOrbstar.vue`)
  - `fetchUsers()`

- **ValuesLeaderboardCard Component** (`src/components/cards/ValuesLeaderboardCard.vue`)
  - `refreshLeaderboard()`

## Future Considerations

- If new list endpoints are added, update the `createEmptyResponse()` function in both files
- Consider consolidating the two axios instances if possible to reduce code duplication
- Monitor for any edge cases where the empty data structure might not match expectations

## Migration Notes

No changes are required in existing components or stores. The interceptors handle the conversion transparently, making this a non-breaking change for the frontend application.
