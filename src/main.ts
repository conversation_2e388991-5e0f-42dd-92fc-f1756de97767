/**
 * main.ts
 *
 * Bootstraps Vuetify and other plugins then mounts the App`
 */

// Components
import App from './App.vue';

// Composables
import { createApp, nextTick } from 'vue';

// Plugins
import { registerPlugins } from '@/plugins';

const app = createApp(App);

registerPlugins(app);

app.mount('#app');

// Initialize auth store and subscriptions after app is mounted
nextTick(async () => {
  try {
    // Import auth store after Pinia is initialized
    const { useAuthStore } = await import('@/store/auth');

    // Import subscriptions to ensure localStorage sync
    await import('@/store/utils/subscriptions');

    // Import test utilities in development
    if (import.meta.env.DEV) {
      await import('@/utils/auth-test');
    }

    // Initialize auth store and start token renewal if authenticated
    const authStore = useAuthStore();
    if (authStore.isAuthenticated && authStore.accessToken) {
      // Start proactive token renewal
      authStore.scheduleTokenRenewal();
    }
  } catch (error) {
    console.error('Failed to initialize auth store:', error);
  }
});
