/**
 * Authentication testing utilities for PKCE flow
 * These functions can be used in the browser console to test the auth implementation
 */

import { useAuthStore } from '@/store/auth';

/**
 * Test the PKCE session exchange with a mock session ID
 * This simulates what happens when the user returns from Okta
 */
export async function testPKCESessionExchange(mockSessionId: string = 'test-session-123') {
  console.log('🧪 Testing PKCE Session Exchange...');
  
  try {
    const authStore = useAuthStore();
    
    // Simulate URL with session parameter
    const originalUrl = window.location.href;
    const testUrl = `${window.location.origin}${window.location.pathname}?session=${mockSessionId}`;
    
    console.log(`📝 Simulating callback URL: ${testUrl}`);
    
    // Update URL without triggering navigation
    window.history.pushState({}, '', testUrl);
    
    // Test the callback initialization
    const success = await authStore.initializeFromCallback();
    
    // Restore original URL
    window.history.pushState({}, '', originalUrl);
    
    if (success) {
      console.log('✅ PKCE Session Exchange: SUCCESS');
      console.log('🔑 Access Token:', authStore.accessToken ? 'Present' : 'Missing');
      console.log('🔄 Refresh Token:', authStore.refreshToken ? 'Present' : 'Missing');
      console.log('👤 Authenticated:', authStore.isAuthenticated);
    } else {
      console.log('❌ PKCE Session Exchange: FAILED');
    }
    
    return success;
  } catch (error) {
    console.error('💥 PKCE Session Exchange Error:', error);
    return false;
  }
}

/**
 * Test token renewal functionality
 */
export async function testTokenRenewal() {
  console.log('🧪 Testing Token Renewal...');
  
  try {
    const authStore = useAuthStore();
    
    if (!authStore.isAuthenticated) {
      console.log('❌ Cannot test token renewal: User not authenticated');
      return false;
    }
    
    console.log('🔄 Attempting token refresh...');
    const result = await authStore.refreshTokens();
    
    if (result) {
      console.log('✅ Token Renewal: SUCCESS');
      console.log('🔑 New Access Token:', result.access ? 'Received' : 'Missing');
      console.log('🔄 New Refresh Token:', result.refresh ? 'Received' : 'Missing');
    } else {
      console.log('❌ Token Renewal: FAILED');
    }
    
    return !!result;
  } catch (error) {
    console.error('💥 Token Renewal Error:', error);
    return false;
  }
}

/**
 * Test proactive token renewal scheduling
 */
export function testProactiveRenewal() {
  console.log('🧪 Testing Proactive Token Renewal...');
  
  try {
    const authStore = useAuthStore();
    
    if (!authStore.isAuthenticated) {
      console.log('❌ Cannot test proactive renewal: User not authenticated');
      return false;
    }
    
    // Check if token needs renewal
    const needsRenewal = authStore.needsTokenRenewal();
    console.log('⏰ Token needs renewal:', needsRenewal);
    
    // Schedule renewal
    authStore.scheduleTokenRenewal();
    console.log('📅 Proactive renewal scheduled');
    
    // Check if timeout is set
    const hasTimeout = authStore.tokenRenewalTimeout !== null;
    console.log('⏲️ Renewal timeout active:', hasTimeout);
    
    return true;
  } catch (error) {
    console.error('💥 Proactive Renewal Error:', error);
    return false;
  }
}

/**
 * Test logout functionality
 */
export function testLogout() {
  console.log('🧪 Testing Logout...');
  
  try {
    const authStore = useAuthStore();
    
    console.log('📊 Before logout:');
    console.log('  - Authenticated:', authStore.isAuthenticated);
    console.log('  - Access Token:', authStore.accessToken ? 'Present' : 'Missing');
    console.log('  - Refresh Token:', authStore.refreshToken ? 'Present' : 'Missing');
    
    authStore.logout();
    
    console.log('📊 After logout:');
    console.log('  - Authenticated:', authStore.isAuthenticated);
    console.log('  - Access Token:', authStore.accessToken ? 'Present' : 'Missing');
    console.log('  - Refresh Token:', authStore.refreshToken ? 'Present' : 'Missing');
    
    // Check localStorage
    const accessTokenInStorage = localStorage.getItem('access_token');
    const refreshTokenInStorage = localStorage.getItem('refresh_token');
    
    console.log('💾 LocalStorage after logout:');
    console.log('  - Access Token:', accessTokenInStorage ? 'Present' : 'Cleared');
    console.log('  - Refresh Token:', refreshTokenInStorage ? 'Present' : 'Cleared');
    
    const success = !authStore.isAuthenticated && !accessTokenInStorage && !refreshTokenInStorage;
    console.log(success ? '✅ Logout: SUCCESS' : '❌ Logout: FAILED');
    
    return success;
  } catch (error) {
    console.error('💥 Logout Error:', error);
    return false;
  }
}

/**
 * Run all authentication tests
 */
export async function runAllAuthTests() {
  console.log('🚀 Running All Authentication Tests...');
  console.log('=====================================');
  
  const results = {
    pkceExchange: false,
    tokenRenewal: false,
    proactiveRenewal: false,
    logout: false
  };
  
  // Test PKCE (this will fail without backend, but tests the flow)
  results.pkceExchange = await testPKCESessionExchange();
  
  // Test proactive renewal
  results.proactiveRenewal = testProactiveRenewal();
  
  // Test token renewal (this will fail without backend, but tests the flow)
  results.tokenRenewal = await testTokenRenewal();
  
  // Test logout
  results.logout = testLogout();
  
  console.log('=====================================');
  console.log('📊 Test Results Summary:');
  console.log('  - PKCE Exchange:', results.pkceExchange ? '✅' : '❌');
  console.log('  - Token Renewal:', results.tokenRenewal ? '✅' : '❌');
  console.log('  - Proactive Renewal:', results.proactiveRenewal ? '✅' : '❌');
  console.log('  - Logout:', results.logout ? '✅' : '❌');
  
  return results;
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).authTests = {
    testPKCESessionExchange,
    testTokenRenewal,
    testProactiveRenewal,
    testLogout,
    runAllAuthTests
  };
  
  console.log('🧪 Auth test utilities loaded! Use window.authTests in console.');
}
